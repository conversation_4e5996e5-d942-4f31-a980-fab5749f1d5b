use std::io::stdout;

use anyhow::Result;
use tracing::Level;
use tracing_appender::{
    non_blocking::WorkerGuard,
    rolling::{RollingFileAppender, Rotation},
};

/// 简单的日志工厂
pub struct LoggerFactory;

impl LoggerFactory {
    /// 创建标准输出日志
    pub fn create_stdout_logger(level: Level) -> Result<Vec<WorkerGuard>> {
        let (writer, guard) = tracing_appender::non_blocking(stdout());

        tracing_subscriber::fmt()
            .with_writer(writer)
            .with_file(true)
            .with_line_number(true)
            .with_target(false)
            .with_ansi(true)
            .with_max_level(level)
            .pretty()
            .init();

        Ok(vec![guard])
    }

    /// 创建文件日志
    pub fn create_file_logger(path: &str, filename: &str, level: Level) -> Result<Vec<WorkerGuard>> {
        let appender = RollingFileAppender::builder()
            .filename_prefix(filename)
            .rotation(Rotation::DAILY)
            .build(path)
            .map_err(|e| anyhow::anyhow!("Failed to create file appender: {}", e))?;

        let (writer, guard) = tracing_appender::non_blocking(appender);

        tracing_subscriber::fmt()
            .with_writer(writer)
            .with_file(true)
            .with_line_number(true)
            .with_target(false)
            .with_ansi(false)
            .with_max_level(level)
            .init();

        Ok(vec![guard])
    }

    /// 创建开发环境日志（stdout + 详细信息）
    pub fn create_dev_logger() -> Result<Vec<WorkerGuard>> {
        Self::create_stdout_logger(Level::DEBUG)
    }

    /// 创建生产环境日志（文件 + 基本信息）
    pub fn create_prod_logger(log_dir: &str) -> Result<Vec<WorkerGuard>> {
        Self::create_file_logger(log_dir, "app.log", Level::INFO)
    }
}

/// 日志写入器工厂
pub struct LogWriterFactory;

impl LogWriterFactory {
    /// 创建标准输出写入器
    pub fn create_stdout_writer() -> Result<(NonBlocking, WorkerGuard)> {
        Ok(tracing_appender::non_blocking(stdout()))
    }

    /// 创建文件写入器
    pub fn create_file_writer(
        path: &str,
        prefix: &str,
        rotation: Rotation,
    ) -> Result<(NonBlocking, WorkerGuard)> {
        let appender = RollingFileAppender::builder()
            .filename_prefix(prefix)
            .rotation(rotation)
            .build(path)
            .map_err(|e| anyhow::anyhow!("Failed to create file appender: {}", e))?;

        Ok(tracing_appender::non_blocking(appender))
    }
}

/// 日志系统构建器
///
/// # 重要限制
///
/// 当前实现由于 tracing-subscriber 类型系统的复杂性，只支持一个主要的输出目标：
///
/// - 如果配置了 `stdout`，则只使用 stdout 输出
/// - 如果只配置了文件，则只使用第一个文件配置
/// - 其他配置会被创建但不会实际使用
///
/// # 示例
///
/// ```rust
/// // 只会输出到 stdout，文件配置被忽略
/// let _guards = TelemetryBuilder::new()
///     .add_stdout(Level::INFO)           // ✅ 被使用
///     .add_file_log("logs/", "app.log", Level::DEBUG)  // ❌ 被忽略
///     .init()?;
///
/// // 只会输出到第一个文件，第二个文件配置被忽略
/// let _guards = TelemetryBuilder::new()
///     .add_file_log("logs/", "debug.log", Level::DEBUG)  // ✅ 被使用
///     .add_file_log("logs/", "error.log", Level::ERROR)  // ❌ 被忽略
///     .init()?;
/// ```
pub struct TelemetryBuilder {
    stdout_config: Option<LogConfig>,
    file_configs: Vec<LogConfig>,
}

impl TelemetryBuilder {
    /// 创建新的构建器
    pub fn new() -> Self {
        Self {
            stdout_config: None,
            file_configs: Vec::new(),
        }
    }

    /// 初始化真正的多目标日志系统（实验性功能）
    ///
    /// 这个方法使用 MultiWriter 来实现真正的多目标输出，
    /// 但所有目标会使用相同的日志级别和格式。
    pub fn init_multi_target(self) -> Result<Vec<WorkerGuard>> {
        let mut guards = Vec::new();
        let mut multi_writer = MultiWriter::new();
        let mut max_level = Level::ERROR; // 默认最严格的级别

        // 添加 stdout 写入器
        if let Some(stdout_config) = &self.stdout_config {
            multi_writer = multi_writer.add_writer(stdout());
            max_level = std::cmp::min(max_level, stdout_config.level);
        }

        // 添加所有文件写入器
        for file_config in &self.file_configs {
            if let LogTarget::File { path, prefix } = &file_config.target {
                let appender = RollingFileAppender::builder()
                    .filename_prefix(prefix)
                    .rotation(Rotation::DAILY)
                    .build(path)
                    .map_err(|e| anyhow::anyhow!("Failed to create file appender: {}", e))?;

                multi_writer = multi_writer.add_writer(appender);
                max_level = std::cmp::min(max_level, file_config.level);
            }
        }

        // 使用 non_blocking 包装 MultiWriter
        let (non_blocking_writer, guard) = tracing_appender::non_blocking(multi_writer);
        guards.push(guard);

        // 初始化 subscriber
        tracing_subscriber::fmt()
            .with_writer(non_blocking_writer)
            .with_file(true)
            .with_line_number(true)
            .with_target(false)
            .with_ansi(false) // 多目标时禁用 ANSI，因为文件不需要颜色
            .with_max_level(max_level)
            .init();

        Ok(guards)
    }

    /// 添加标准输出日志
    pub fn add_stdout(mut self, level: Level) -> Self {
        self.stdout_config = Some(LogConfig {
            target: LogTarget::Stdout,
            level,
            ..Default::default()
        });
        self
    }

    /// 添加文件日志
    pub fn add_file_log(mut self, path: &str, prefix: &str, level: Level) -> Self {
        self.file_configs.push(LogConfig {
            target: LogTarget::File {
                path: path.to_string(),
                prefix: prefix.to_string(),
            },
            level,
            with_ansi: false, // 文件日志不需要颜色
            ..Default::default()
        });
        self
    }

    /// 构建并初始化日志系统 - 支持多个输出目标
    pub fn init(self) -> Result<Vec<WorkerGuard>> {
        let mut guards = Vec::new();

        // 检查配置情况
        let has_stdout = self.stdout_config.is_some();
        let has_files = !self.file_configs.is_empty();

        match (has_stdout, has_files) {
            (false, false) => {
                // 没有任何配置，使用默认配置
                tracing_subscriber::fmt()
                    .with_file(true)
                    .with_line_number(true)
                    .with_target(false)
                    .with_ansi(true)
                    .with_max_level(Level::INFO)
                    .pretty()
                    .init();
            }
            (true, false) => {
                // 只有 stdout 配置
                let stdout_config = self.stdout_config.unwrap();
                let (stdout_writer, stdout_guard) = LogWriterFactory::create_stdout_writer()?;
                guards.push(stdout_guard);

                tracing_subscriber::fmt()
                    .with_writer(stdout_writer)
                    .with_file(stdout_config.with_file)
                    .with_line_number(stdout_config.with_line_number)
                    .with_target(stdout_config.with_target)
                    .with_ansi(stdout_config.with_ansi)
                    .with_max_level(stdout_config.level)
                    .pretty()
                    .init();
            }
            (false, true) => {
                // 只有文件配置，使用第一个文件作为主要输出
                let file_config = &self.file_configs[0];
                if let LogTarget::File { path, prefix } = &file_config.target {
                    let (file_writer, file_guard) =
                        LogWriterFactory::create_file_writer(path, prefix, Rotation::DAILY)?;
                    guards.push(file_guard);

                    tracing_subscriber::fmt()
                        .with_writer(file_writer)
                        .with_file(file_config.with_file)
                        .with_line_number(file_config.with_line_number)
                        .with_target(file_config.with_target)
                        .with_ansi(file_config.with_ansi)
                        .with_max_level(file_config.level)
                        .init();
                }

                // 为其他文件配置创建额外的写入器（但不能同时初始化多个 subscriber）
                // 这里我们只能记录 guards，实际的多目标输出需要更复杂的实现
                for file_config in self.file_configs.iter().skip(1) {
                    if let LogTarget::File { path, prefix } = &file_config.target {
                        let (_file_writer, file_guard) =
                            LogWriterFactory::create_file_writer(path, prefix, Rotation::DAILY)?;
                        guards.push(file_guard);
                        // 注意：这里创建了写入器但没有使用，这是当前实现的限制
                    }
                }
            }
            (true, true) => {
                // 同时有 stdout 和文件配置，优先使用 stdout
                let stdout_config = self.stdout_config.unwrap();
                let (stdout_writer, stdout_guard) = LogWriterFactory::create_stdout_writer()?;
                guards.push(stdout_guard);

                tracing_subscriber::fmt()
                    .with_writer(stdout_writer)
                    .with_file(stdout_config.with_file)
                    .with_line_number(stdout_config.with_line_number)
                    .with_target(stdout_config.with_target)
                    .with_ansi(stdout_config.with_ansi)
                    .with_max_level(stdout_config.level)
                    .pretty()
                    .init();

                // 为文件配置创建写入器（但当前无法同时使用）
                for file_config in self.file_configs {
                    if let LogTarget::File { path, prefix } = &file_config.target {
                        let (_file_writer, file_guard) =
                            LogWriterFactory::create_file_writer(path, prefix, Rotation::DAILY)?;
                        guards.push(file_guard);
                        // 注意：这里创建了写入器但没有使用，这是当前实现的限制
                    }
                }
            }
        }

        Ok(guards)
    }
}

impl Default for TelemetryBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// 便捷的初始化函数 - 默认配置
pub fn tracing_init() -> Result<Vec<WorkerGuard>> {
    TelemetryBuilder::new()
        .add_stdout(Level::INFO)
        .init()
}

/// 便捷的初始化函数 - 开发环境配置
pub fn tracing_init_dev() -> Result<Vec<WorkerGuard>> {
    TelemetryBuilder::new()
        .add_stdout(Level::DEBUG)
        .init()
}

/// 便捷的初始化函数 - 生产环境配置
pub fn tracing_init_prod() -> Result<Vec<WorkerGuard>> {
    TelemetryBuilder::new()
        .add_file_log("logs/", "app.log", Level::INFO)
        .init()
}

/// 便捷的初始化函数 - 自定义配置示例
pub fn tracing_init_custom() -> Result<Vec<WorkerGuard>> {
    TelemetryBuilder::new()
        .add_stdout(Level::DEBUG)
        .add_file_log("logs/", "debug.log", Level::DEBUG)
        .add_file_log("logs/", "error.log", Level::ERROR)
        .init()
}

/// 便捷的初始化函数 - 真正的多目标输出（实验性）
///
/// 这个函数演示如何使用 MultiWriter 实现真正的多目标日志输出。
/// 所有配置的目标都会接收到日志，但使用相同的格式和最严格的日志级别。
pub fn tracing_init_multi_target() -> Result<Vec<WorkerGuard>> {
    TelemetryBuilder::new()
        .add_stdout(Level::INFO)
        .add_file_log("logs/", "debug.log", Level::DEBUG)
        .add_file_log("logs/", "error.log", Level::ERROR)
        .init_multi_target()
}


