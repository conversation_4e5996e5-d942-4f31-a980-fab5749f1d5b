use std::io::stdout;

use anyhow::Result;
use tracing_appender::{
    non_blocking::WorkerGuard,
    rolling::{RollingFileAppender, Rotation},
};
use tracing_subscriber::util::SubscriberInitExt;

pub fn tracing_init() -> Result<Vec<WorkerGuard>> {
    let mut guards = vec![];

    // 创建 stdout writer
    let (stdout_writer, stdout_guard) = tracing_appender::non_blocking(stdout());
    guards.push(stdout_guard);

    // 创建 info log file writer
    let info_appender = RollingFileAppender::builder()
        .filename_prefix("info.log")
        .rotation(Rotation::DAILY)
        .build("logs/")
        .expect("failed to initialize info rolling file appender");
    let (info_writer, info_guard) = tracing_appender::non_blocking(info_appender);
    guards.push(info_guard);

    // 创建 warn log file writer
    let warn_appender = RollingFileAppender::builder()
        .filename_prefix("warn.log")
        .rotation(Rotation::DAILY)
        .build("logs/")
        .expect("failed to initialize warn rolling file appender");
    let (warn_writer, warn_guard) = tracing_appender::non_blocking(warn_appender);
    guards.push(warn_guard);

    // 创建 subscriber
    tracing_subscriber::fmt()
        .with_file(true)
        .with_line_number(true)
        .with_level(true)
        .with_target(false)
        .with_writer(stdout_writer)
        .with_ansi(true)
        .pretty()
        .init();

    Ok(guards)
}


