use std::io::stdout;

use anyhow::Result;
use tracing::{Level, level_filters::LevelFilter};
use tracing_appender::{
    non_blocking::{self, NonBlocking, WorkerGuard},
    rolling::{RollingFileAppender, Rotation},
};
use tracing_subscriber::{
    Layer, Registry, fmt::format::Format, layer::SubscriberExt, util::SubscriberInitExt,
};

pub fn tracing_init() -> Result<Vec<WorkerGuard>> {
    let format = Format::default()
        .with_file(true)
        .with_line_number(true)
        .with_level(true)
        .with_target(false);

    let mut guards = vec![];
    
    // 创建一个 tracing_subscriber::registry
    let subscriber = tracing_subscriber::registry::Registry::default();
    
    // 收集所有的 layers 和 guards
    let mut layers = Vec::new();
    
    for (path, level) in [
        ("stdout", Level::INFO),
        ("logs/", Level::INFO),
        ("logs/", Level::WARN),
    ] {
        let (guard, layer) =
            get_layers::<tracing_subscriber::fmt::Layer<Registry>>(path, level, format.clone());
        layers.push(layer);
        guards.push(guard);
    }
    
    // 使用 with 方法链式调用添加所有 layers
    let subscriber = layers.into_iter().fold(subscriber, |subscriber, layer| {
        subscriber.with(layer)
    });
    
    // 初始化 subscriber
    subscriber.init();

    Ok(guards)
}

pub fn get_layers<T>(
    path: &str,
    level: Level,
    format: Format,
) -> (WorkerGuard, impl Layer<Registry>) {
    if path.eq("stdout") {
        let (non_blocking, guard) = tracing_appender::non_blocking(stdout());
        let layer = tracing_subscriber::fmt::Layer::new()
            .event_format(format)
            .with_writer(non_blocking)
            .with_ansi(true)
            .pretty();
        (guard, layer.boxed())
    } else {
        let x = RollingFileAppender::builder()
            .filename_prefix(format!("{level}.log"))
            .rotation(Rotation::DAILY)
            .build(path)
            .expect("failed to initialize rolling file appender");
        let (non_blocking, guard) = tracing_appender::non_blocking(x);
        let layer = tracing_subscriber::fmt::Layer::new()
            .event_format(format)
            .with_writer(non_blocking)
            .with_filter(LevelFilter::from_level(level));
        (guard, layer.boxed())
    }

    // Layer::default()
    //     .with_file(true)
    //     .with_level(true)
    //     .with_line_number(true)
    //     .with_ansi(true)
    //     .with_writer(make_writer)
    //     .pretty()
}
