use std::io::stdout;

use anyhow::Result;
use tracing::Level;
use tracing_appender::{
    non_blocking::{NonBlocking, WorkerGuard},
    rolling::{RollingFileAppender, Rotation},
};


/// 日志输出目标类型
#[derive(Debug, <PERSON>lone)]
pub enum LogTarget {
    Stdout,
    File { path: String, prefix: String },
}

/// 日志配置
#[derive(Debug, Clone)]
pub struct LogConfig {
    pub target: LogTarget,
    pub level: Level,
    pub with_file: bool,
    pub with_line_number: bool,
    pub with_target: bool,
    pub with_ansi: bool,
    pub pretty: bool,
}

impl Default for LogConfig {
    fn default() -> Self {
        Self {
            target: LogTarget::Stdout,
            level: Level::INFO,
            with_file: true,
            with_line_number: true,
            with_target: false,
            with_ansi: true,
            pretty: true,
        }
    }
}

/// 日志写入器工厂
pub struct LogWriterFactory;

impl LogWriterFactory {
    /// 创建标准输出写入器
    pub fn create_stdout_writer() -> Result<(NonBlocking, WorkerGuard)> {
        Ok(tracing_appender::non_blocking(stdout()))
    }

    /// 创建文件写入器
    pub fn create_file_writer(
        path: &str,
        prefix: &str,
        rotation: Rotation,
    ) -> Result<(NonBlocking, WorkerGuard)> {
        let appender = RollingFileAppender::builder()
            .filename_prefix(prefix)
            .rotation(rotation)
            .build(path)
            .map_err(|e| anyhow::anyhow!("Failed to create file appender: {}", e))?;

        Ok(tracing_appender::non_blocking(appender))
    }
}

/// 日志系统构建器
pub struct TelemetryBuilder {
    stdout_config: Option<LogConfig>,
    file_configs: Vec<LogConfig>,
}

impl TelemetryBuilder {
    /// 创建新的构建器
    pub fn new() -> Self {
        Self {
            stdout_config: None,
            file_configs: Vec::new(),
        }
    }

    /// 添加标准输出日志
    pub fn add_stdout(mut self, level: Level) -> Self {
        self.stdout_config = Some(LogConfig {
            target: LogTarget::Stdout,
            level,
            ..Default::default()
        });
        self
    }

    /// 添加文件日志
    pub fn add_file_log(mut self, path: &str, prefix: &str, level: Level) -> Self {
        self.file_configs.push(LogConfig {
            target: LogTarget::File {
                path: path.to_string(),
                prefix: prefix.to_string(),
            },
            level,
            with_ansi: false, // 文件日志不需要颜色
            ..Default::default()
        });
        self
    }

    /// 构建并初始化日志系统
    pub fn init(self) -> Result<Vec<WorkerGuard>> {
        let mut guards = Vec::new();

        // 简化实现：只支持一个主要的输出目标
        if let Some(stdout_config) = self.stdout_config {
            // 使用 stdout 作为主要输出
            let (stdout_writer, stdout_guard) = LogWriterFactory::create_stdout_writer()?;
            guards.push(stdout_guard);

            tracing_subscriber::fmt()
                .with_writer(stdout_writer)
                .with_file(stdout_config.with_file)
                .with_line_number(stdout_config.with_line_number)
                .with_target(stdout_config.with_target)
                .with_ansi(stdout_config.with_ansi)
                .with_max_level(stdout_config.level)
                .pretty()
                .init();
        } else if let Some(file_config) = self.file_configs.first() {
            // 使用第一个文件配置作为主要输出
            if let LogTarget::File { path, prefix } = &file_config.target {
                let (file_writer, file_guard) =
                    LogWriterFactory::create_file_writer(path, prefix, Rotation::DAILY)?;
                guards.push(file_guard);

                tracing_subscriber::fmt()
                    .with_writer(file_writer)
                    .with_file(file_config.with_file)
                    .with_line_number(file_config.with_line_number)
                    .with_target(file_config.with_target)
                    .with_ansi(file_config.with_ansi)
                    .with_max_level(file_config.level)
                    .init();
            }
        } else {
            // 默认配置
            tracing_subscriber::fmt()
                .with_file(true)
                .with_line_number(true)
                .with_target(false)
                .with_ansi(true)
                .with_max_level(Level::INFO)
                .pretty()
                .init();
        }

        Ok(guards)
    }
}

impl Default for TelemetryBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// 便捷的初始化函数 - 默认配置
pub fn tracing_init() -> Result<Vec<WorkerGuard>> {
    TelemetryBuilder::new()
        .add_stdout(Level::INFO)
        .init()
}

/// 便捷的初始化函数 - 开发环境配置
pub fn tracing_init_dev() -> Result<Vec<WorkerGuard>> {
    TelemetryBuilder::new()
        .add_stdout(Level::DEBUG)
        .init()
}

/// 便捷的初始化函数 - 生产环境配置
pub fn tracing_init_prod() -> Result<Vec<WorkerGuard>> {
    TelemetryBuilder::new()
        .add_file_log("logs/", "app.log", Level::INFO)
        .init()
}

/// 便捷的初始化函数 - 自定义配置示例
pub fn tracing_init_custom() -> Result<Vec<WorkerGuard>> {
    TelemetryBuilder::new()
        .add_stdout(Level::DEBUG)
        .add_file_log("logs/", "debug.log", Level::DEBUG)
        .add_file_log("logs/", "error.log", Level::ERROR)
        .init()
}


