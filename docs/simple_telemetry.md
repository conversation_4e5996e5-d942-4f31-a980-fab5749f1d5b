# 简单工厂模式日志系统

## 概述

这是一个使用简单工厂模式实现的日志系统，专注于简洁性和易用性。

## 设计原则

- **简洁性**：只有 82 行代码，易于理解和维护
- **实用性**：覆盖最常见的日志需求
- **清晰性**：每个方法职责单一，命名清晰
- **类型安全**：编译时检查，避免运行时错误

## 核心组件

### LoggerFactory (日志工厂)

```rust
pub struct LoggerFactory;

impl LoggerFactory {
    // 创建标准输出日志
    pub fn create_stdout_logger(level: Level) -> Result<Vec<WorkerGuard>>
    
    // 创建文件日志
    pub fn create_file_logger(path: &str, filename: &str, level: Level) -> Result<Vec<WorkerGuard>>
    
    // 创建开发环境日志
    pub fn create_dev_logger() -> Result<Vec<WorkerGuard>>
    
    // 创建生产环境日志
    pub fn create_prod_logger(log_dir: &str) -> Result<Vec<WorkerGuard>>
}
```

### 便捷函数

```rust
// 默认配置 (stdout + INFO)
pub fn tracing_init() -> Result<Vec<WorkerGuard>>

// 开发环境 (stdout + DEBUG)
pub fn tracing_init_dev() -> Result<Vec<WorkerGuard>>

// 生产环境 (文件 + INFO)
pub fn tracing_init_prod() -> Result<Vec<WorkerGuard>>
```

## 使用方式

### 1. 快速开始
```rust
use newsletter::telemetry::tracing_init;

// 最简单的使用方式
let _guards = tracing_init()?;
info!("Hello, world!");
```

### 2. 环境特定配置
```rust
use newsletter::telemetry::{tracing_init_dev, tracing_init_prod};

// 开发环境
let _guards = tracing_init_dev()?;

// 生产环境
let _guards = tracing_init_prod()?;
```

### 3. 自定义配置
```rust
use newsletter::telemetry::LoggerFactory;
use tracing::Level;

// 自定义文件日志
let _guards = LoggerFactory::create_file_logger("logs", "custom.log", Level::WARN)?;

// 自定义标准输出日志
let _guards = LoggerFactory::create_stdout_logger(Level::TRACE)?;
```

## 特性

### ✅ 支持的功能
- 标准输出日志（带颜色和美化格式）
- 文件日志（按日期轮转）
- 可配置的日志级别
- 文件名和行号显示
- 非阻塞异步写入
- 开发和生产环境预设

### ⚠️ 当前限制
- 每次只能初始化一个日志目标
- 不支持同时输出到多个目标
- 格式配置相对固定

### 🎯 适用场景
- 单一输出目标的应用
- 简单的日志需求
- 快速原型开发
- 学习和教学用途
- 微服务应用

## 与复杂版本的对比

| 特性 | 简单工厂模式 | 复杂构建器模式 |
|------|-------------|---------------|
| 代码行数 | 82 行 | 300+ 行 |
| 学习成本 | 低 | 高 |
| 多目标支持 | ❌ | ✅ (有限制) |
| 类型复杂性 | 简单 | 复杂 |
| 维护成本 | 低 | 高 |
| 扩展性 | 中等 | 高 |

## 扩展建议

如果需要更复杂的功能，可以考虑：

### 1. 多目标输出
```rust
// 方案1: 外部工具
// 使用 Fluentd, Vector, Logstash 等日志聚合工具

// 方案2: 应用层分发
// 为不同用途创建不同的 logger 实例

// 方案3: 自定义 MultiWriter
// 实现自己的多目标写入器
```

### 2. 动态配置
```rust
// 使用配置文件
// 支持运行时重新加载配置
```

### 3. 结构化日志
```rust
// 添加 JSON 格式支持
// 支持自定义字段
```

## 最佳实践

1. **选择合适的级别**
   - 开发：DEBUG 或 TRACE
   - 测试：INFO
   - 生产：WARN 或 ERROR

2. **文件日志管理**
   - 定期清理旧日志文件
   - 监控磁盘空间使用

3. **性能考虑**
   - 保持 guards 的生命周期
   - 避免频繁的日志级别切换

4. **错误处理**
   - 妥善处理日志初始化失败
   - 考虑降级策略

## 示例代码

参见 `examples/simple_factory.rs` 获取完整的使用示例。

## 测试

```bash
# 运行示例
cargo run --example simple_factory

# 测试文件日志
cargo run --example simple_factory
ls -la logs/
```
