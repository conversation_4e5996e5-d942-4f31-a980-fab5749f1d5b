# 日志系统设计文档

## 概述

本项目使用工厂模式重新设计了日志系统，提供了灵活、可扩展的日志配置方案。

## 设计模式

### 工厂模式的应用

1. **LogWriterFactory**: 负责创建不同类型的日志写入器
2. **TelemetryBuilder**: 使用构建器模式配置日志系统
3. **LogConfig**: 封装日志配置参数

### 核心组件

#### 1. LogTarget (日志输出目标)
```rust
pub enum LogTarget {
    Stdout,                                    // 标准输出
    File { path: String, prefix: String },    // 文件输出
}
```

#### 2. LogConfig (日志配置)
```rust
pub struct LogConfig {
    pub target: LogTarget,        // 输出目标
    pub level: Level,            // 日志级别
    pub with_file: bool,         // 是否显示文件名
    pub with_line_number: bool,  // 是否显示行号
    pub with_target: bool,       // 是否显示目标模块
    pub with_ansi: bool,         // 是否使用颜色
    pub pretty: bool,            // 是否使用美化格式
}
```

#### 3. LogWriterFactory (写入器工厂)
```rust
impl LogWriterFactory {
    pub fn create_stdout_writer() -> Result<(NonBlocking, WorkerGuard)>
    pub fn create_file_writer(path: &str, prefix: &str, rotation: Rotation) -> Result<(NonBlocking, WorkerGuard)>
}
```

#### 4. TelemetryBuilder (构建器)
```rust
impl TelemetryBuilder {
    pub fn new() -> Self
    pub fn add_stdout(self, level: Level) -> Self
    pub fn add_file_log(self, path: &str, prefix: &str, level: Level) -> Self
    pub fn init(self) -> Result<Vec<WorkerGuard>>
}
```

## 使用方式

### 1. 便捷函数
```rust
// 默认配置 (stdout + INFO)
let _guards = tracing_init()?;

// 开发环境 (stdout + DEBUG)
let _guards = tracing_init_dev()?;

// 生产环境 (文件 + INFO)
let _guards = tracing_init_prod()?;
```

### 2. 构建器模式
```rust
let _guards = TelemetryBuilder::new()
    .add_stdout(Level::DEBUG)
    .add_file_log("logs/", "app.log", Level::INFO)
    .init()?;
```

## 设计优势

### 1. 关注点分离
- **LogWriterFactory**: 专注于创建写入器
- **TelemetryBuilder**: 专注于配置组合
- **LogConfig**: 专注于参数封装

### 2. 可扩展性
- 易于添加新的输出目标类型
- 易于添加新的配置选项
- 易于添加新的预设配置

### 3. 类型安全
- 使用强类型枚举定义输出目标
- 编译时检查配置有效性
- 避免运行时配置错误

### 4. 易用性
- 提供多种便捷函数
- 支持链式调用
- 合理的默认值

## 当前限制

### 1. 单一输出目标
当前实现为了避免复杂的类型问题，只支持一个主要的输出目标。优先级：
1. stdout 配置（如果存在）
2. 第一个文件配置
3. 默认配置

### 2. 简化的过滤
目前使用 `with_max_level` 进行全局级别过滤，而不是每个 layer 独立过滤。

## 未来改进方向

### 1. 多目标支持
- 使用类型擦除技术支持多个输出目标
- 实现更复杂的 layer 组合

### 2. 动态配置
- 支持运行时修改日志级别
- 支持配置文件热重载

### 3. 结构化日志
- 支持 JSON 格式输出
- 支持自定义字段

### 4. 性能优化
- 异步日志写入
- 批量写入优化

## 示例代码

参见 `examples/telemetry_usage.rs` 获取完整的使用示例。

## 测试

```bash
# 编译检查
cargo check

# 运行示例
cargo run --example telemetry_usage

# 查看生成的日志文件
ls -la logs/
```
