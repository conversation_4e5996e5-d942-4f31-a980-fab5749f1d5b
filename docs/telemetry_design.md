# 日志系统设计文档

## 概述

本项目使用工厂模式重新设计了日志系统，提供了灵活、可扩展的日志配置方案。

## 设计模式

### 工厂模式的应用

1. **LogWriterFactory**: 负责创建不同类型的日志写入器
2. **TelemetryBuilder**: 使用构建器模式配置日志系统
3. **LogConfig**: 封装日志配置参数

### 核心组件

#### 1. LogTarget (日志输出目标)
```rust
pub enum LogTarget {
    Stdout,                                    // 标准输出
    File { path: String, prefix: String },    // 文件输出
}
```

#### 2. LogConfig (日志配置)
```rust
pub struct LogConfig {
    pub target: LogTarget,        // 输出目标
    pub level: Level,            // 日志级别
    pub with_file: bool,         // 是否显示文件名
    pub with_line_number: bool,  // 是否显示行号
    pub with_target: bool,       // 是否显示目标模块
    pub with_ansi: bool,         // 是否使用颜色
    pub pretty: bool,            // 是否使用美化格式
}
```

#### 3. LogWriterFactory (写入器工厂)
```rust
impl LogWriterFactory {
    pub fn create_stdout_writer() -> Result<(NonBlocking, WorkerGuard)>
    pub fn create_file_writer(path: &str, prefix: &str, rotation: Rotation) -> Result<(NonBlocking, WorkerGuard)>
}
```

#### 4. TelemetryBuilder (构建器)
```rust
impl TelemetryBuilder {
    pub fn new() -> Self
    pub fn add_stdout(self, level: Level) -> Self
    pub fn add_file_log(self, path: &str, prefix: &str, level: Level) -> Self
    pub fn init(self) -> Result<Vec<WorkerGuard>>
}
```

## 使用方式

### 1. 便捷函数
```rust
// 默认配置 (stdout + INFO)
let _guards = tracing_init()?;

// 开发环境 (stdout + DEBUG)
let _guards = tracing_init_dev()?;

// 生产环境 (文件 + INFO)
let _guards = tracing_init_prod()?;
```

### 2. 构建器模式
```rust
let _guards = TelemetryBuilder::new()
    .add_stdout(Level::DEBUG)
    .add_file_log("logs/", "app.log", Level::INFO)
    .init()?;
```

## 设计优势

### 1. 关注点分离
- **LogWriterFactory**: 专注于创建写入器
- **TelemetryBuilder**: 专注于配置组合
- **LogConfig**: 专注于参数封装

### 2. 可扩展性
- 易于添加新的输出目标类型
- 易于添加新的配置选项
- 易于添加新的预设配置

### 3. 类型安全
- 使用强类型枚举定义输出目标
- 编译时检查配置有效性
- 避免运行时配置错误

### 4. 易用性
- 提供多种便捷函数
- 支持链式调用
- 合理的默认值

## 当前限制

### 1. 多目标支持限制 ⚠️
**重要发现**：当前实现虽然允许配置多个日志目标，但由于 tracing-subscriber 的类型系统复杂性，实际只使用一个主要输出目标：

**优先级规则**：
1. **stdout 配置**（如果存在）- 优先级最高
2. **第一个文件配置** - 如果没有 stdout 配置
3. **默认配置** - 如果没有任何配置

**实际行为**：
```rust
// 这个配置只会使用 debug.log，error.log 会被忽略
let _guards = TelemetryBuilder::new()
    .add_file_log("logs/", "debug.log", Level::DEBUG)
    .add_file_log("logs/", "error.log", Level::ERROR)  // ❌ 被忽略
    .init()?;

// 这个配置只会使用 stdout，文件配置会被忽略
let _guards = TelemetryBuilder::new()
    .add_stdout(Level::INFO)                           // ✅ 被使用
    .add_file_log("logs/", "app.log", Level::DEBUG)   // ❌ 被忽略
    .init()?;
```

### 2. 类型系统复杂性
tracing-subscriber 的 Layer 组合会产生复杂的嵌套类型，导致编译时类型推断困难。

### 3. 全局 Subscriber 限制
tracing 只允许设置一次全局 subscriber，无法在运行时动态重新配置。

## 真正的多目标日志实现

如果你需要真正的多目标日志输出，可以考虑以下方案：

### 方案 1: 使用 tracing-appender 的 MultiWriter
```rust
use tracing_appender::non_blocking;
use std::io::{self, Write};

// 创建一个组合写入器
struct MultiWriter<W1: Write, W2: Write> {
    writer1: W1,
    writer2: W2,
}

impl<W1: Write, W2: Write> Write for MultiWriter<W1, W2> {
    fn write(&mut self, buf: &[u8]) -> io::Result<usize> {
        self.writer1.write_all(buf)?;
        self.writer2.write_all(buf)?;
        Ok(buf.len())
    }

    fn flush(&mut self) -> io::Result<()> {
        self.writer1.flush()?;
        self.writer2.flush()?;
        Ok(())
    }
}
```

### 方案 2: 使用外部日志聚合
- **Fluentd**: 收集和转发日志到多个目标
- **Vector**: 高性能日志路由器
- **Logstash**: Elastic Stack 的日志处理组件

### 方案 3: 应用层日志分发
```rust
// 为不同目的创建不同的 logger
pub fn init_multi_target_logging() -> Result<Vec<WorkerGuard>> {
    // 主应用日志 -> stdout
    let _main_guards = TelemetryBuilder::new()
        .add_stdout(Level::INFO)
        .init()?;

    // 审计日志 -> 专用文件 (需要单独的 tracing 实例)
    // 错误日志 -> 错误监控系统

    Ok(vec![]) // 简化示例
}
```

## 未来改进方向

### 1. 高级多目标支持
- 研究 tracing-subscriber 的高级 Layer 组合技术
- 实现类型安全的多目标配置
- 支持每个目标独立的过滤规则

### 2. 动态配置
- 支持运行时修改日志级别
- 支持配置文件热重载
- 支持通过 API 动态调整日志行为

### 3. 结构化日志
- 支持 JSON 格式输出
- 支持自定义字段和上下文
- 支持结构化查询和分析

### 4. 性能优化
- 异步日志写入
- 批量写入优化
- 内存池和缓冲区管理

### 5. 监控和可观测性
- 日志统计和指标
- 日志健康检查
- 与 OpenTelemetry 集成

## 示例代码

参见 `examples/telemetry_usage.rs` 获取完整的使用示例。

## 测试

```bash
# 编译检查
cargo check

# 运行示例
cargo run --example telemetry_usage

# 查看生成的日志文件
ls -la logs/
```
