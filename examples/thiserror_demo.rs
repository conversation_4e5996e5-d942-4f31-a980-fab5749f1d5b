use std::str::FromStr;

use strum::{AsRefStr, EnumString, IntoStaticStr};

fn main() {
    let environment: Environment = std::env::var("APP_ENVIRONMENT")
        .map(|s| s.parse().unwrap_or_default())
        .unwrap_or_default();

    println!("{environment:?}")
}

#[derive(PartialEq, Eq, EnumString, IntoStaticStr, AsRefStr, Default, Debug)]
#[strum(serialize_all = "snake_case")]
pub enum Environment {
    #[default]
    Local,
    Production,
}
