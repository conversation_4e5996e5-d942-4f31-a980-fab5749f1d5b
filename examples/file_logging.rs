use anyhow::Result;
use tracing::{debug, error, info, warn};

use newsletter::telemetry::TelemetryBuilder;

#[tokio::main]
async fn main() -> Result<()> {
    println!("=== 文件日志示例 ===\n");

    // 确保 logs 目录存在
    std::fs::create_dir_all("logs")?;

    // 使用文件日志配置
    let _guards = TelemetryBuilder::new()
        .add_file_log("logs/", "example.log", tracing::Level::DEBUG)
        .init()?;

    println!("日志将写入到 logs/example.log 文件中...");

    // 写入不同级别的日志
    debug!("这是一条 DEBUG 级别的日志");
    info!("这是一条 INFO 级别的日志");
    warn!("这是一条 WARN 级别的日志");
    error!("这是一条 ERROR 级别的日志");

    // 模拟一些应用活动
    for i in 1..=5 {
        info!("处理任务 {}", i);
        if i == 3 {
            warn!("任务 {} 遇到警告", i);
        }
        if i == 4 {
            error!("任务 {} 发生错误", i);
        }
    }

    println!("日志已写入完成！");
    println!("请检查 logs/example.log 文件查看日志内容。");

    // 显示文件内容（如果存在）
    if let Ok(content) = std::fs::read_to_string("logs/example.log") {
        println!("\n=== 日志文件内容预览 ===");
        println!("{}", content);
    }

    Ok(())
}
