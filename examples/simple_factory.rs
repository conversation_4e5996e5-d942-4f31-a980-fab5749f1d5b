use anyhow::Result;
use tracing::{debug, error, info, warn, Level};

use newsletter::telemetry::{LoggerFactory, tracing_init, tracing_init_dev, tracing_init_prod};

#[tokio::main]
async fn main() -> Result<()> {
    println!("=== 简单工厂模式日志示例 ===\n");

    // 确保 logs 目录存在
    std::fs::create_dir_all("logs")?;

    println!("1. 使用便捷函数:");
    println!("   tracing_init()      - 默认配置 (stdout + INFO)");
    println!("   tracing_init_dev()  - 开发配置 (stdout + DEBUG)");
    println!("   tracing_init_prod() - 生产配置 (文件 + INFO)");

    println!("\n2. 使用工厂方法:");
    println!("   LoggerFactory::create_stdout_logger(Level::INFO)");
    println!("   LoggerFactory::create_file_logger(\"logs\", \"app.log\", Level::INFO)");
    println!("   LoggerFactory::create_dev_logger()");
    println!("   LoggerFactory::create_prod_logger(\"logs\")");

    println!("\n3. 初始化日志系统 (使用默认配置):");
    let _guards = tracing_init()?;

    println!("\n4. 测试日志输出:");
    debug!("这是 DEBUG 日志 - 不会显示 (当前级别是 INFO)");
    info!("这是 INFO 日志 - 会显示");
    warn!("这是 WARN 日志 - 会显示");
    error!("这是 ERROR 日志 - 会显示");

    println!("\n=== 简单工厂模式的优势 ===");
    println!("✅ 代码简洁：只有 82 行代码");
    println!("✅ 易于理解：清晰的工厂方法");
    println!("✅ 功能完整：支持 stdout 和文件输出");
    println!("✅ 环境适配：提供开发和生产环境预设");
    println!("✅ 类型安全：编译时检查");
    println!("✅ 无复杂性：没有复杂的类型组合");

    println!("\n=== 使用场景 ===");
    println!("• 单一输出目标的应用");
    println!("• 简单的日志需求");
    println!("• 快速原型开发");
    println!("• 学习和教学用途");

    println!("\n=== 扩展建议 ===");
    println!("如果需要多目标输出，可以考虑：");
    println!("• 使用外部日志聚合工具 (Fluentd, Vector)");
    println!("• 应用层日志分发");
    println!("• 为不同用途创建不同的 logger 实例");

    Ok(())
}
