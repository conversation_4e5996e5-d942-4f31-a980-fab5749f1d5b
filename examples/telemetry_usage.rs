use anyhow::Result;
use tracing::{debug, error, info, warn};

use newsletter::telemetry::tracing_init;

#[tokio::main]
async fn main() -> Result<()> {
    println!("=== 日志系统工厂模式使用示例 ===\n");

    // 确保 logs 目录存在
    std::fs::create_dir_all("logs").ok();

    // 示例 1: 使用默认配置
    println!("1. 使用默认配置 (stdout + INFO 级别):");
    let _guards = tracing_init()?;

    info!("这是一条 INFO 级别的日志");
    warn!("这是一条 WARN 级别的日志");
    error!("这是一条 ERROR 级别的日志");
    debug!("这条 DEBUG 日志不会显示，因为级别是 INFO");

    println!("\n2. 构建器模式的配置选项:");
    println!("   - TelemetryBuilder::new().add_stdout(Level::DEBUG)");
    println!("   - TelemetryBuilder::new().add_file_log(\"logs/\", \"app.log\", Level::INFO)");

    println!("\n3. 不同环境的预设配置函数:");
    println!("   - tracing_init()      // 默认: stdout + INFO");
    println!("   - tracing_init_dev()  // 开发: stdout + DEBUG");
    println!("   - tracing_init_prod() // 生产: 文件 + INFO");

    println!("\n4. 测试不同级别的日志:");
    info!("INFO 级别日志 - 会显示");
    warn!("WARN 级别日志 - 会显示");
    error!("ERROR 级别日志 - 会显示");
    debug!("DEBUG 级别日志 - 不会显示（当前级别是 INFO）");

    println!("\n=== 工厂模式的优势 ===");
    println!("✓ 关注点分离：LogWriterFactory 专注创建写入器");
    println!("✓ 可扩展性：易于添加新的输出目标和配置");
    println!("✓ 类型安全：编译时检查配置有效性");
    println!("✓ 易用性：提供便捷函数和构建器模式");

    println!("\n=== 使用建议 ===");
    println!("• 开发环境：使用 tracing_init_dev() 获得详细日志");
    println!("• 生产环境：使用 tracing_init_prod() 输出到文件");
    println!("• 自定义需求：使用 TelemetryBuilder 构建器模式");

    println!("\n注意：tracing 只能初始化一次，所以这个示例只展示了默认配置的效果。");
    println!("在实际应用中，根据环境选择合适的初始化函数即可。");

    Ok(())
}
