use anyhow::Result;
use tracing::{debug, error, info, warn};

use newsletter::telemetry::tracing_init_prod;

#[tokio::main]
async fn main() -> Result<()> {
    println!("=== 简单文件日志测试 ===\n");

    // 使用生产环境配置（文件日志）
    let _guards = tracing_init_prod()?;

    println!("日志将写入到 logs/app.log 文件中...");

    // 写入不同级别的日志
    debug!("这是 DEBUG 日志 - 不会写入 (级别是 INFO)");
    info!("这是 INFO 日志 - 会写入文件");
    warn!("这是 WARN 日志 - 会写入文件");
    error!("这是 ERROR 日志 - 会写入文件");

    // 模拟一些应用活动
    for i in 1..=3 {
        info!("处理任务 {}", i);
        if i == 2 {
            warn!("任务 {} 遇到警告", i);
        }
        if i == 3 {
            error!("任务 {} 发生错误", i);
        }
    }

    // 等待日志写入完成
    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

    println!("日志写入完成！");
    
    // 检查并显示日志文件
    if let Ok(entries) = std::fs::read_dir("logs/") {
        println!("\n生成的日志文件:");
        for entry in entries {
            if let Ok(entry) = entry {
                let path = entry.path();
                if let Some(name) = path.file_name() {
                    if let Some(name_str) = name.to_str() {
                        if name_str.starts_with("app.log") {
                            println!("  📁 {}", name_str);
                            
                            // 显示文件内容
                            if let Ok(content) = std::fs::read_to_string(&path) {
                                let line_count = content.lines().count();
                                println!("     ✅ {} 行日志", line_count);
                                
                                println!("     内容预览:");
                                for (i, line) in content.lines().take(3).enumerate() {
                                    println!("     {}. {}", i + 1, line);
                                }
                                if line_count > 3 {
                                    println!("     ... (还有 {} 行)", line_count - 3);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    println!("\n✅ 简单工厂模式的文件日志功能正常工作！");

    Ok(())
}
