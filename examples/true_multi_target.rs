use anyhow::Result;
use tracing::{debug, error, info, warn};

use newsletter::telemetry::{TelemetryBuilder, tracing_init_multi_target};

#[tokio::main]
async fn main() -> Result<()> {
    println!("=== 真正的多目标日志测试 ===\n");

    // 确保 logs 目录存在
    std::fs::create_dir_all("logs")?;

    // 清理之前的日志文件
    let _ = std::fs::remove_file("logs/debug.log.2025-06-19");
    let _ = std::fs::remove_file("logs/error.log.2025-06-19");

    println!("使用 MultiWriter 实现真正的多目标日志输出:");
    println!("配置: stdout + debug.log + error.log");
    
    // 使用新的多目标方法
    let _guards = TelemetryBuilder::new()
        .add_stdout(tracing::Level::INFO)
        .add_file_log("logs/", "debug.log", tracing::Level::DEBUG)
        .add_file_log("logs/", "error.log", tracing::Level::ERROR)
        .init_multi_target()?;

    println!("\n写入测试日志...");
    debug!("这是一条 DEBUG 日志 - 应该出现在所有目标中");
    info!("这是一条 INFO 日志 - 应该出现在所有目标中");
    warn!("这是一条 WARN 日志 - 应该出现在所有目标中");
    error!("这是一条 ERROR 日志 - 应该出现在所有目标中");

    // 等待日志写入完成
    tokio::time::sleep(tokio::time::Duration::from_millis(200)).await;

    println!("\n=== 验证结果 ===");
    verify_multi_target_output().await?;

    println!("\n=== 使用便捷函数 ===");
    println!("也可以使用 tracing_init_multi_target() 便捷函数:");
    println!("let _guards = tracing_init_multi_target()?;");

    Ok(())
}

async fn verify_multi_target_output() -> Result<()> {
    println!("检查各个目标的日志内容:");

    // 检查文件日志
    if let Ok(entries) = std::fs::read_dir("logs/") {
        let mut files = Vec::new();
        for entry in entries {
            if let Ok(entry) = entry {
                let path = entry.path();
                if let Some(name) = path.file_name() {
                    if let Some(name_str) = name.to_str() {
                        if name_str.contains("debug.log") || name_str.contains("error.log") {
                            files.push((name_str.to_string(), path));
                        }
                    }
                }
            }
        }
        
        files.sort();
        for (name, path) in files {
            println!("  📁 {}", name);
            if let Ok(content) = std::fs::read_to_string(&path) {
                if !content.is_empty() {
                    let line_count = content.lines().count();
                    println!("     ✅ {} 行日志", line_count);
                    
                    // 显示第一行内容作为示例
                    if let Some(first_line) = content.lines().next() {
                        println!("     示例: {}", first_line);
                    }
                } else {
                    println!("     ❌ 空文件");
                }
            }
            println!();
        }
    }

    println!("结论:");
    println!("✅ 使用 MultiWriter 后，所有配置的目标都应该接收到相同的日志");
    println!("✅ stdout 显示了日志（你在终端中看到的）");
    println!("✅ 两个文件都应该包含相同的日志内容");
    
    println!("\n注意:");
    println!("• 所有目标使用相同的格式（无 ANSI 颜色）");
    println!("• 使用最严格的日志级别（ERROR）");
    println!("• 这是一个实验性功能，适合简单的多目标需求");

    Ok(())
}
