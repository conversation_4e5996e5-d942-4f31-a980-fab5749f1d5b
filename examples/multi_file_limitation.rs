use anyhow::Result;
use tracing::{error, info, warn};

use newsletter::telemetry::LoggerFactory;

#[tokio::main]
async fn main() -> Result<()> {
    println!("=== 多文件写入限制演示 ===\n");

    // 确保 logs 目录存在
    std::fs::create_dir_all("logs")?;

    println!("尝试创建多个文件日志...");

    // 第一个文件日志
    println!("1. 创建第一个文件日志: debug.log");
    let _guards1 = LoggerFactory::create_file_logger("logs", "debug.log", tracing::Level::DEBUG)?;
    
    info!("写入到 debug.log 的日志");
    warn!("这条日志应该在 debug.log 中");

    // 尝试创建第二个文件日志 - 这会失败！
    println!("\n2. 尝试创建第二个文件日志: error.log");
    match LoggerFactory::create_file_logger("logs", "error.log", tracing::Level::ERROR) {
        Ok(_guards2) => {
            println!("   ✅ 成功创建第二个日志");
            error!("这条日志应该在 error.log 中");
        }
        Err(e) => {
            println!("   ❌ 失败: {}", e);
            println!("   原因: tracing 只允许初始化一次全局 subscriber");
        }
    }

    // 等待日志写入完成
    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

    println!("\n=== 检查生成的文件 ===");
    if let Ok(entries) = std::fs::read_dir("logs/") {
        for entry in entries {
            if let Ok(entry) = entry {
                let path = entry.path();
                if let Some(name) = path.file_name() {
                    if let Some(name_str) = name.to_str() {
                        if name_str.contains("debug.log") || name_str.contains("error.log") {
                            println!("📁 {}", name_str);
                            if let Ok(content) = std::fs::read_to_string(&path) {
                                if !content.is_empty() {
                                    println!("   ✅ {} 行日志", content.lines().count());
                                } else {
                                    println!("   ❌ 空文件");
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    println!("\n=== 结论 ===");
    println!("❌ 简单工厂模式不支持多文件写入");
    println!("✅ 只有第一个初始化的日志目标会工作");
    println!("💡 这是 tracing 全局 subscriber 的限制");

    println!("\n=== 解决方案 ===");
    println!("如果需要多文件日志，可以考虑：");
    println!("1. 使用外部日志聚合工具 (Fluentd, Vector)");
    println!("2. 应用层日志分发策略");
    println!("3. 实现自定义 MultiWriter (如之前的复杂版本)");
    println!("4. 为不同模块使用不同的日志实例");

    Ok(())
}
