use anyhow::Result;
use tracing::{debug, error, info, warn};

use newsletter::telemetry::TelemetryBuilder;

#[tokio::main]
async fn main() -> Result<()> {
    println!("=== 多目标日志测试 ===\n");

    // 确保 logs 目录存在
    std::fs::create_dir_all("logs")?;

    println!("测试场景: 配置多个文件日志");
    test_multiple_files().await?;

    println!("\n=== 测试结果分析 ===");
    analyze_results().await?;

    println!("\n=== 当前实现的限制 ===");
    println!("由于 tracing-subscriber 的类型系统复杂性，当前实现有以下限制：");
    println!("1. 当配置多个文件时，只有第一个文件会实际接收日志");
    println!("2. 当同时配置 stdout 和文件时，优先使用 stdout");
    println!("3. 其他配置的写入器会被创建但不会被使用");

    println!("\n=== 解决方案建议 ===");
    println!("1. 对于真正的多目标日志，建议使用更高级的配置");
    println!("2. 或者为不同的用途创建不同的 logger 实例");
    println!("3. 当前的工厂模式更适合单一主要输出目标的场景");
    println!("4. 如需多目标，可以考虑使用外部日志聚合工具");

    Ok(())
}

async fn test_multiple_files() -> Result<()> {
    println!("配置: debug.log (DEBUG) + error.log (ERROR)");
    
    // 注意：这里只会使用第一个文件配置 (debug.log)
    let _guards = TelemetryBuilder::new()
        .add_file_log("logs/", "debug.log", tracing::Level::DEBUG)
        .add_file_log("logs/", "error.log", tracing::Level::ERROR)
        .init()?;

    println!("写入日志到文件...");
    debug!("这是一条 DEBUG 日志");
    info!("这是一条 INFO 日志");
    warn!("这是一条 WARN 日志");
    error!("这是一条 ERROR 日志");

    // 等待一下确保日志写入
    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

    // 检查生成的文件
    println!("检查生成的日志文件:");
    if let Ok(entries) = std::fs::read_dir("logs/") {
        for entry in entries {
            if let Ok(entry) = entry {
                let path = entry.path();
                if let Some(name) = path.file_name() {
                    if let Some(name_str) = name.to_str() {
                        if name_str.starts_with("debug.log") || name_str.starts_with("error.log") {
                            println!("  - {}", name_str);
                            
                            // 显示文件内容
                            if let Ok(content) = std::fs::read_to_string(&path) {
                                if !content.is_empty() {
                                    println!("    内容: {} 行", content.lines().count());
                                } else {
                                    println!("    内容: 空文件");
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    Ok(())
}

async fn analyze_results() -> Result<()> {
    println!("分析生成的日志文件:");

    if let Ok(entries) = std::fs::read_dir("logs/") {
        let mut files = Vec::new();
        for entry in entries {
            if let Ok(entry) = entry {
                let path = entry.path();
                if let Some(name) = path.file_name() {
                    if let Some(name_str) = name.to_str() {
                        if name_str.contains("debug.log") || name_str.contains("error.log") {
                            files.push((name_str.to_string(), path));
                        }
                    }
                }
            }
        }

        files.sort();
        for (name, path) in files {
            println!("  📁 {}", name);
            if let Ok(content) = std::fs::read_to_string(&path) {
                if !content.is_empty() {
                    println!("     ✅ {} 行日志", content.lines().count());
                    // 显示前几行内容
                    for (i, line) in content.lines().take(2).enumerate() {
                        println!("     {}. {}", i + 1, line);
                    }
                    if content.lines().count() > 2 {
                        println!("     ... (还有 {} 行)", content.lines().count() - 2);
                    }
                } else {
                    println!("     ❌ 空文件 (说明没有接收到日志)");
                }
            }
            println!();
        }
    }

    println!("结论:");
    println!("✅ debug.log 接收到了所有日志（因为它是第一个配置）");
    println!("❌ error.log 是空的（因为只使用了第一个文件配置）");

    Ok(())
}
